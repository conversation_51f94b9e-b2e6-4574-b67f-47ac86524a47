import React, { useEffect } from "react";
import { observer } from "mobx-react-lite";
import { useStore } from "../../contexts/StoreContext";
import DeckComponent from "../Deck/DeckComponent";
import { Link } from "react-router-dom";
import { CrossfaderComponent } from "../Mixer/CrossfaderComponent";
import { availableCurves } from "../../utils/crossfaderCurves";
import { RootStoreType } from "@/stores/RootStore"; // For type checking rootStore
import MasterDeckControls from "../Deck/MasterDeckControls";

const AllDecksPage: React.FC = observer(() => {
  const rootStore = useStore() as RootStoreType; // Ensure rootStore is typed for full access
  const { settingsStore, decks } = rootStore;

  // Handle main crossfader
  useEffect(() => {
    const { crossfaderValue, crossfaderCurve } = settingsStore;
    const selectedCurveObject = availableCurves.find(
      (c) => c.name === crossfaderCurve,
    );

    if (!selectedCurveObject) {
      console.warn(`Crossfader curve "${crossfaderCurve}" not found.`);
      // Optionally, apply a default linear curve or no crossfade effect
      decks.forEach((deck) => {
        if (deck.audioEngine) {
          deck.audioEngine.setCrossfaderGain(1.0); // Full gain if curve not found
        }
      });
      return;
    }

    const curveFunc = selectedCurveObject.curve;
    const [gainLeft, gainRight] = curveFunc(crossfaderValue);

    decks.forEach((deck) => {
      if (deck.audioEngine) {
        // Determine if the deck is on the left or right side
        // Deck 1 and Deck 3 are on the left
        // Deck 2 and Deck 4 are on the right
        const deckNumber = parseInt(deck.id.split("-")[1]);
        if (deckNumber === 1 || deckNumber === 3) {
          deck.audioEngine.setCrossfaderGain(gainLeft);
        } else if (deckNumber === 2 || deckNumber === 4) {
          deck.audioEngine.setCrossfaderGain(gainRight);
        }
      }
    });
  }, [
    settingsStore.crossfaderValue,
    settingsStore.crossfaderCurve,
    decks,
    settingsStore,
  ]);

  // Handle per-band crossfaders
  useEffect(() => {
    if (!settingsStore.enablePerBandCrossfaders) return;

    const {
      lowCrossfaderValue,
      midCrossfaderValue,
      midLoCrossfaderValue,
      midHiCrossfaderValue,
      highCrossfaderValue,
      crossfaderCurve,
      eqBands
    } = settingsStore;

    const selectedCurveObject = availableCurves.find(
      (c) => c.name === crossfaderCurve,
    );

    if (!selectedCurveObject) {
      console.warn(`Crossfader curve "${crossfaderCurve}" not found for per-band crossfaders.`);
      return;
    }

    const curveFunc = selectedCurveObject.curve;
    const [lowGainLeft, lowGainRight] = curveFunc(lowCrossfaderValue);
    const [midGainLeft, midGainRight] = curveFunc(midCrossfaderValue);
    const [midLoGainLeft, midLoGainRight] = curveFunc(midLoCrossfaderValue);
    const [midHiGainLeft, midHiGainRight] = curveFunc(midHiCrossfaderValue);
    const [highGainLeft, highGainRight] = curveFunc(highCrossfaderValue);

    decks.forEach((deck) => {
      if (deck.audioEngine) {
        // Determine if the deck is on the left or right side
        const deckNumber = parseInt(deck.id.split("-")[1]);
        const isLeftDeck = deckNumber === 1 || deckNumber === 3;

        deck.audioEngine.setBandCrossfaderGain(
          deck.lowEQ,
          deck.midEQ,
          deck.highEQ,
          deck.midLoEQ,
          deck.midHiEQ,
          isLeftDeck ? lowGainLeft : lowGainRight,
          isLeftDeck ? midGainLeft : midGainRight,
          isLeftDeck ? highGainLeft : highGainRight,
          eqBands === "4-band" ? (isLeftDeck ? midLoGainLeft : midLoGainRight) : 0,
          eqBands === "4-band" ? (isLeftDeck ? midHiGainLeft : midHiGainRight) : 0,
        );
      }
    });
  }, [
    settingsStore.enablePerBandCrossfaders,
    settingsStore.lowCrossfaderValue,
    settingsStore.midCrossfaderValue,
    settingsStore.midLoCrossfaderValue,
    settingsStore.midHiCrossfaderValue,
    settingsStore.highCrossfaderValue,
    settingsStore.crossfaderCurve,
    settingsStore.eqBands,
    decks,
    settingsStore,
  ]);

  if (decks.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-center">
        <p className="mb-2 text-lg">No decks are active.</p>
        <Link
          to="/settings"
          className="text-blue-600 hover:text-blue-800 underline"
        >
          Go to Settings to enable decks.
        </Link>
      </div>
    );
  }

  const getDeckById = (id: string) => decks.find((d) => d.id === id);

  const deck1 = getDeckById("deck-1");
  const deck2 = getDeckById("deck-2");
  const deck3 = getDeckById("deck-3");
  const deck4 = getDeckById("deck-4");

  const activeDecksCount = decks.length;

  // Helper to render a deck with its title
  const renderDeck = (
    deckInstance: ReturnType<typeof getDeckById>,
    deckNumber: number,
  ) => {
    if (!deckInstance) {
      // Show a placeholder if the specific deck (e.g. deck-3 for a 4-deck layout) isn't found in the active decks
      // This might happen if settingsStore.numberOfDecks is 4, but RootStore.decks doesn't yet contain deck-3
      return (
        <div className="flex-1 p-3 border rounded-lg bg-muted/50 shadow-sm flex items-center justify-center min-h-[200px]">
          <p className="text-muted-foreground">
            Deck {deckNumber} not available
          </p>
        </div>
      );
    }
    return (
      <div className="flex-1 p-3 border rounded-lg bg-card shadow-sm min-h-[200px] flex flex-col">
        <h2 className="text-xl font-semibold mb-3 text-center text-card-foreground">
          Deck {deckNumber}
        </h2>
        <div className="flex-grow">
          <DeckComponent deck={deckInstance} />
        </div>
      </div>
    );
  };

  const shouldRenderRightColumn =
    activeDecksCount > 1 || (activeDecksCount === 1 && !deck1);

  return (
    <div className="flex flex-col h-[calc(100vh-theme_header_height)] p-2 gap-2 bg-background text-foreground overflow-hidden">
      {/* Decks Container */}
      <div className="flex flex-row flex-grow gap-2 overflow-hidden">
        {/* Left Column: Always rendered if there's at least one deck */}
        {activeDecksCount > 0 && (
          <div
            className={`flex flex-col w-full ${shouldRenderRightColumn ? "md:w-1/2" : "md:w-full"} space-y-2 overflow-y-auto pr-1 custom-scrollbar`}
          >
            {deck1
              ? renderDeck(deck1, 1)
              : activeDecksCount >= 1 && renderDeck(undefined, 1)}
            {activeDecksCount === 4 &&
              (deck3 ? renderDeck(deck3, 3) : renderDeck(undefined, 3))}
          </div>
        )}

        {/* Right Column: Rendered if more than 1 deck, or if only 1 deck is active and it's not deck-1 (edge case) */}
        {shouldRenderRightColumn && (
          <div className="flex flex-col w-full md:w-1/2 space-y-2 overflow-y-auto pl-1 custom-scrollbar">
            {/* Render Deck 2 if 2 or 4 decks are active */}
            {(activeDecksCount === 2 || activeDecksCount === 4) &&
              (deck2 ? renderDeck(deck2, 2) : renderDeck(undefined, 2))}

            {/* Render Deck 4 only if 4 decks are active */}
            {activeDecksCount === 4 &&
              (deck4 ? renderDeck(deck4, 4) : renderDeck(undefined, 4))}
          </div>
        )}
      </div>

      {/* Mixer Controls Container - only render if at least one deck is active */}
      {activeDecksCount > 0 && (
        <div className="flex-shrink-0 mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
          {/* Master Deck Controls */}
          <div className="bg-card rounded-lg shadow-md">
            <MasterDeckControls />
          </div>

          {/* Crossfader */}
          <div className="bg-card rounded-lg shadow-md p-2">
            <CrossfaderComponent />
          </div>
        </div>
      )}
    </div>
  );
});

export default AllDecksPage;
