import { types, Instance, getRoot, flow } from "mobx-state-tree";
import { DeckStoreInstance } from "./DeckStore";
import { RootStoreType } from "./RootStore";

export const SyncStoreModel = types
  .model("SyncStore", {
    // Current master deck ID (null if no master or using ghost master)
    masterDeckId: types.maybeNull(types.string),
    // Ghost master tempo for when no deck is master
    ghostMasterBpm: types.optional(types.number, 120),
    // Audio level monitoring for auto master selection
    audioLevelMonitoringEnabled: types.optional(types.boolean, true),
  })
  .volatile<{
    // Audio level monitoring data
    deckAudioLevels: Map<string, number>;
    // Debounce timers for master switching
    masterSwitchDebounceTimers: Map<string, NodeJS.Timeout>;
  }>(() => ({
    deckAudioLevels: new Map(),
    masterSwitchDebounceTimers: new Map(),
  }))
  .actions((self) => {
    // Set a deck as master (manual selection)
    const setMasterDeck = (deckId: string | null) => {
      const rootStore = getRoot<RootStoreType>(self);
      
      // Clear current master
      if (self.masterDeckId) {
        const currentMaster = rootStore.decks.find(d => d.id === self.masterDeckId);
        if (currentMaster) {
          currentMaster.setIsMaster(false);
        }
      }
      
      // Set new master
      self.masterDeckId = deckId;
      if (deckId) {
        const newMaster = rootStore.decks.find(d => d.id === deckId);
        if (newMaster) {
          newMaster.setIsMaster(true);
          // Update ghost master BPM to match new master
          if (newMaster.currentBpm > 0) {
            self.ghostMasterBpm = newMaster.currentBpm;
          }
        }
      }
      
      console.log(`Master deck set to: ${deckId || 'none (ghost master)'}`);
    };

    // Get the current master deck instance
    const getMasterDeck = (): DeckStoreInstance | null => {
      if (!self.masterDeckId) return null;
      const rootStore = getRoot<RootStoreType>(self);
      return rootStore.decks.find(d => d.id === self.masterDeckId) || null;
    };

    // Get the effective master BPM (from master deck or ghost master)
    const getEffectiveMasterBpm = (): number => {
      const masterDeck = getMasterDeck();
      return masterDeck?.currentBpm || self.ghostMasterBpm;
    };

    // Check if a deck is suitable to be master
    const isDeckSuitableForMaster = (deck: DeckStoreInstance): boolean => {
      if (!deck.loadedTrack || !deck.isPlaying) return false;
      if (deck.currentBpm <= 0) return false; // No valid BPM data
      
      // Check remaining time (>10 seconds OR looping)
      const remainingTime = deck.effectiveDuration - deck.currentTime;
      if (remainingTime < 10) {
        // TODO: Check if track is looping (need to add loop property to deck)
        return false;
      }
      
      return true;
    };

    // Find the best candidate for master deck based on audio levels
    const findBestMasterCandidate = (): DeckStoreInstance | null => {
      const rootStore = getRoot<RootStoreType>(self);
      const suitableDecks = rootStore.decks.filter(isDeckSuitableForMaster);
      
      if (suitableDecks.length === 0) return null;
      
      // Find deck with highest audio level
      let bestDeck = suitableDecks[0];
      let highestLevel = self.deckAudioLevels.get(bestDeck.id) || 0;
      
      for (const deck of suitableDecks.slice(1)) {
        const level = self.deckAudioLevels.get(deck.id) || 0;
        if (level > highestLevel) {
          highestLevel = level;
          bestDeck = deck;
        }
      }
      
      return bestDeck;
    };

    // Auto master mode logic - called when deck states change
    const updateAutoMaster = () => {
      const rootStore = getRoot<RootStoreType>(self);
      if (!rootStore.settingsStore.autoMasterMode) return;
      
      const currentMaster = getMasterDeck();
      
      // If no master, try to find one
      if (!currentMaster) {
        const candidate = findBestMasterCandidate();
        if (candidate) {
          setMasterDeck(candidate.id);
        }
        return;
      }
      
      // Check if current master should lose status
      if (!isDeckSuitableForMaster(currentMaster)) {
        const candidate = findBestMasterCandidate();
        setMasterDeck(candidate?.id || null);
      }
    };

    // Update audio level for a deck (called by audio monitoring)
    const updateDeckAudioLevel = (deckId: string, level: number) => {
      self.deckAudioLevels.set(deckId, level);
      
      // Trigger auto master update if enabled
      const rootStore = getRoot<RootStoreType>(self);
      if (rootStore.settingsStore.autoMasterMode) {
        updateAutoMaster();
      }
    };

    // Sync a deck to the current master
    const syncDeckToMaster = (deckId: string) => {
      const rootStore = getRoot<RootStoreType>(self);
      const deck = rootStore.decks.find(d => d.id === deckId);
      if (!deck || !deck.loadedTrack) return false;
      
      const masterBpm = getEffectiveMasterBpm();
      if (masterBpm <= 0) return false;
      
      // Check sync constraints
      const maxPitchRange = rootStore.settingsStore.maxSyncPitchRange;
      const targetRate = masterBpm / deck.originalBpm;
      
      if (maxPitchRange !== "unlimited") {
        const maxRange = parseInt(maxPitchRange) / 100; // Convert percentage to decimal
        const minRate = 1 - maxRange;
        const maxRate = 1 + maxRange;
        
        if (targetRate < minRate || targetRate > maxRate) {
          console.warn(`Sync failed: Target rate ${targetRate.toFixed(2)} outside allowed range [${minRate.toFixed(2)}, ${maxRate.toFixed(2)}]`);
          return false;
        }
      }
      
      // Apply BPM sync
      deck.setPlaybackRate(targetRate);
      
      // TODO: Implement phase alignment based on syncGranularity setting
      // This would require beat/bar position tracking and alignment logic
      
      console.log(`Deck ${deckId} synced to master BPM ${masterBpm} (rate: ${targetRate.toFixed(2)})`);
      return true;
    };

    return {
      setMasterDeck,
      getMasterDeck,
      getEffectiveMasterBpm,
      isDeckSuitableForMaster,
      findBestMasterCandidate,
      updateAutoMaster,
      updateDeckAudioLevel,
      syncDeckToMaster,
    };
  })
  .views((self) => ({
    get hasMasterDeck() {
      return self.masterDeckId !== null;
    },
    
    get isUsingGhostMaster() {
      return self.masterDeckId === null;
    },
  }));

export interface SyncStoreInstance extends Instance<typeof SyncStoreModel> {}
