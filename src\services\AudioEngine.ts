// src/services/AudioEngine.ts
import { getSharedAudioContext } from './AudioManager';
import { RootStoreType } from '../stores/RootStore';
import { DeckStoreInstance } from '../stores/DeckStore';
import { getTrackFileHandle } from '../utils/fileHandleUtils';
import { DeckPath } from './AudioRoutingManager';

/**
 * Interface for audio buffer cache entries
 */
interface AudioBufferCacheEntry {
  buffer: AudioBuffer;
  lastAccessed: number;
}

/**
 * Audio engine for a single deck - now uses the new AudioRoutingManager
 */
export class DeckAudioEngine {
  // Static cache for audio buffers
  private static audioBufferCache = new Map<string, AudioBufferCacheEntry>();
  private static readonly MAX_CACHE_SIZE = 10; // Maximum number of cached buffers

  private audioContext: AudioContext;
  private rootStore: RootStoreType;
  private deckStore: DeckStoreInstance;

  // Audio routing
  private deckPath: DeckPath | null = null;
  private sourceNode: AudioBufferSourceNode | null = null;

  // Audio buffer and playback state
  private audioBuffer: AudioBuffer | null = null;
  private isPlaying: boolean = false;
  private playbackStartTime: number = 0;
  private playbackOffset: number = 0;
  private animationFrameId: number | null = null;

  // Track currently being loaded
  private _loadingTrackPromise: { [trackId: string]: Promise<void> | null } = {};

  constructor(rootStore: RootStoreType, deckStore: DeckStoreInstance) {
    this.rootStore = rootStore;
    this.deckStore = deckStore;
    this.audioContext = getSharedAudioContext();

    // Get the deck path from the audio routing manager
    this.initializeDeckPath();
  }

  /**
   * Initialize the deck path from the audio routing manager
   */
  private initializeDeckPath(): void {
    try {
      this.deckPath = this.rootStore.audioRoutingManager.getDeckPath(this.deckStore.id);
      if (!this.deckPath) {
        console.warn(`DeckAudioEngine: No deck path found for deck ${this.deckStore.id}`);
      } else {
        console.log(`DeckAudioEngine: Initialized with deck path for deck ${this.deckStore.id}`);
      }
    } catch (error) {
      console.error(`DeckAudioEngine: Failed to get deck path for deck ${this.deckStore.id}:`, error);
    }
  }

  /**
   * Ensure the deck path is available and initialized
   */
  private ensureDeckPath(): boolean {
    if (!this.deckPath) {
      this.initializeDeckPath();
    }
    return this.deckPath !== null;
  }

  /**
   * Check if audio buffer is loaded
   */
  public isBufferLoaded(): boolean {
    return this.audioBuffer !== null;
  }

  /**
   * Load a track's audio buffer
   */
  public async loadTrack(trackId: string): Promise<void> {
    // If this track is already being loaded, return the existing promise
    if (this._loadingTrackPromise[trackId]) {
      console.log(`DeckAudioEngine: Already loading track ${trackId}, returning existing promise`);
      return this._loadingTrackPromise[trackId]!;
    }

    // Create a new loading promise
    this._loadingTrackPromise[trackId] = this._loadTrackInternal(trackId);

    try {
      await this._loadingTrackPromise[trackId];
    } finally {
      // Clean up the promise reference when done
      delete this._loadingTrackPromise[trackId];
    }
  }

  /**
   * Internal method to load a track's audio buffer
   */
  private async _loadTrackInternal(trackId: string): Promise<void> {
    // Check if we already have this buffer in cache
    if (DeckAudioEngine.audioBufferCache.has(trackId)) {
      const cacheEntry = DeckAudioEngine.audioBufferCache.get(trackId)!;
      this.audioBuffer = cacheEntry.buffer;
      cacheEntry.lastAccessed = Date.now();
      console.log(`DeckAudioEngine: Loaded track ${trackId} from cache`);
      
      // Set the audio buffer in the deck path
      if (this.ensureDeckPath()) {
        this.deckPath!.setAudioBuffer(this.audioBuffer);
      }
      return;
    }

    // Load the track from file
    const fileHandle = await getTrackFileHandle(trackId);
    if (!fileHandle) {
      throw new Error(`No file handle found for track ${trackId}`);
    }

    const file = await fileHandle.getFile();
    const arrayBuffer = await file.arrayBuffer();

    // Decode audio data
    console.log(`DeckAudioEngine: Decoding audio data for track ${trackId}...`);
    this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
    console.log(`DeckAudioEngine: Successfully decoded track ${trackId}, duration: ${this.audioBuffer.duration}s`);

    // Cache the buffer
    DeckAudioEngine.audioBufferCache.set(trackId, {
      buffer: this.audioBuffer,
      lastAccessed: Date.now()
    });

    // Set the audio buffer in the deck path
    if (this.ensureDeckPath()) {
      this.deckPath!.setAudioBuffer(this.audioBuffer);
    }

    // Manage cache size
    this.manageCache();

    console.log(`DeckAudioEngine: Loaded track ${trackId}`);
  }

  /**
   * Manage the size of the audio buffer cache
   */
  private manageCache(): void {
    if (DeckAudioEngine.audioBufferCache.size <= DeckAudioEngine.MAX_CACHE_SIZE) {
      return;
    }

    // Find the oldest entry
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of DeckAudioEngine.audioBufferCache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    // Remove the oldest entry
    if (oldestKey) {
      DeckAudioEngine.audioBufferCache.delete(oldestKey);
      console.log(`DeckAudioEngine: Removed track ${oldestKey} from cache`);
    }
  }

  /**
   * Play the loaded audio buffer
   */
  public async play(): Promise<void> {
    console.log("DeckAudioEngine: play called");
    if (!this.audioBuffer) {
      console.warn('Cannot play: No audio buffer loaded');
      return;
    }

    if (!this.ensureDeckPath()) {
      console.error('Cannot play: No deck path available');
      return;
    }

    // Stop any existing playback
    if (this.sourceNode) {
      this.sourceNode.stop();
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }

    try {
      // Create a new source node
      this.sourceNode = this.deckPath!.createSource();
      
      // Enable master tempo if needed
      if (this.deckStore.masterTempoEnabled) {
        this.deckPath!.enableMasterTempo(this.deckStore.playbackRate);
      } else {
        this.deckPath!.disableMasterTempo();
      }

      // Start playback
      this.startPlayback();
    } catch (error) {
      console.error('Error starting playback:', error);
    }
  }

  /**
   * Helper method to start the actual playback after nodes are connected
   */
  private startPlayback(): void {
    // Calculate start time and offset
    this.playbackOffset = this.deckStore.currentTime;
    this.playbackStartTime = this.audioContext.currentTime;

    // Start playback from the current position
    this.sourceNode!.start(0, this.playbackOffset);

    // Update state
    this.isPlaying = true;

    // Start the time update loop
    this.startTimeUpdateLoop();

    console.log(`DeckAudioEngine: Started playback at ${this.playbackOffset}s with buffer duration ${this.audioBuffer!.duration}s`);
  }

  /**
   * Pause playback
   */
  public pause(): void {
    console.log(`DeckAudioEngine: Pausing playback at ${this.deckStore.currentTime}s`);
    if (!this.isPlaying || !this.sourceNode) {
      return;
    }

    // Stop the source node
    try {
      this.sourceNode.stop();
    } catch (error) {
      // Source might already be stopped
      console.warn('Error stopping source node:', error);
    }

    this.sourceNode.disconnect();
    this.sourceNode = null;

    // Update state
    this.isPlaying = false;

    // Stop the time update loop
    this.stopTimeUpdateLoop();

    console.log(`DeckAudioEngine: Paused playback at ${this.deckStore.currentTime}s`);
  }

  /**
   * Seek to a specific time
   */
  public seek(timeInSeconds: number): void {
    const wasPlaying = this.isPlaying;

    this.pause();

    // Update the current time
    this.deckStore.setCurrentTime(timeInSeconds);
    this.playbackOffset = timeInSeconds;

    if (wasPlaying) {
      this.play();
    }

    console.log(`DeckAudioEngine: Seeked to ${timeInSeconds}s`);
  }

  /**
   * Set the volume for the deck's main gain node
   */
  public setVolume(volume: number): void {
    if (this.ensureDeckPath()) {
      this.deckPath!.setVolume(volume);
    }
  }

  /**
   * Set the gain for the main crossfader node
   */
  public setCrossfaderGain(gain: number): void {
    if (this.ensureDeckPath()) {
      this.deckPath!.setCrossfaderGain(gain);
    }
  }
